#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库迁移脚本

执行以下步骤：
1. 备份material_items和material_libs表的数据
2. 更新material_libs表
3. 更新material_items表（从CSV导入）
4. 更新material_items_material_lib_links关联表

作者: Assistant
日期: 2024
"""

import os
import sys
import logging
import argparse
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class PostgreSQLMigrator:
    """PostgreSQL数据库迁移器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.conn = None
        
        # 供应商映射
        self.vendors = [
            {'id': 1, 'name': '<PERSON>yan', 'description': '乐研 - 71.82%'},
            {'id': 2, 'name': '<PERSON><PERSON>', 'description': '麦克林 - 10.64%'},
            {'id': 3, 'name': 'BidePharm', 'description': '百灵威 - 6.33%'},
            {'id': 4, 'name': 'Aladdin', 'description': '阿拉丁 - 5.97%'},
            {'id': 5, 'name': 'Merck', 'description': '默克 - 2.47%'},
            {'id': 6, 'name': 'Rhawn', 'description': '罗恩 - 1.52%'},
            {'id': 7, 'name': 'TCI', 'description': '东京化成 - 1.05%'},
            {'id': 8, 'name': 'Reagent', 'description': '试剂 - 0.19%'}
        ]
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(
                host=self.config['DB_HOST'],
                port=self.config['DB_PORT'],
                database=self.config['DB_NAME'],
                user=self.config['DB_USER'],
                password=self.config['DB_PASSWORD']
            )
            self.conn.autocommit = False
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            self.logger.info("数据库连接已关闭")
    
    def backup_tables(self):
        """备份现有表数据到本地文件"""
        self.logger.info("开始备份表数据到本地文件...")
        
        # 创建备份目录
        if 'BACKUP_DIR' in self.config and self.config['BACKUP_DIR']:
            backup_base = Path(self.config['BACKUP_DIR'])
        else:
            backup_base = Path('.')
        
        backup_dir = backup_base / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            with self.conn.cursor() as cur:
                # 备份material_items表
                self.logger.info("备份material_items表...")
                items_backup_file = backup_dir / "material_items_backup.csv"
                with open(items_backup_file, 'w', encoding='utf-8') as f:
                    cur.copy_expert(
                        "COPY material_items TO STDOUT WITH CSV HEADER DELIMITER ','", 
                        f
                    )
                self.logger.info(f"material_items表已备份到: {items_backup_file}")
                
                # 备份material_libs表
                self.logger.info("备份material_libs表...")
                libs_backup_file = backup_dir / "material_libs_backup.csv"
                with open(libs_backup_file, 'w', encoding='utf-8') as f:
                    cur.copy_expert(
                        "COPY material_libs TO STDOUT WITH CSV HEADER DELIMITER ','", 
                        f
                    )
                self.logger.info(f"material_libs表已备份到: {libs_backup_file}")
                
                self.logger.info(f"表备份完成，备份文件保存在: {backup_dir.absolute()}")
                
        except Exception as e:
            self.logger.error(f"备份失败: {e}")
            raise
    
    def update_material_libs(self):
        """更新material_libs表"""
        self.logger.info("开始更新material_libs表...")
        
        try:
            with self.conn.cursor() as cur:
                # 清空表并重置序列
                cur.execute("TRUNCATE TABLE material_libs RESTART IDENTITY CASCADE;")
                
                # 插入新的供应商数据
                insert_sql = """
                    INSERT INTO material_libs (id, name, version, description, status, 
                                             last_update_time, created_at, updated_at) 
                    VALUES %s
                """
                
                values = [
                    (vendor['id'], vendor['name'], '1.0', vendor['description'], 
                     'active', datetime.now(), datetime.now(), datetime.now())
                    for vendor in self.vendors
                ]
                
                execute_values(cur, insert_sql, values)
                
                # 重置序列
                cur.execute("SELECT setval('material_libs_id_seq', 8);")
                
                self.conn.commit()
                self.logger.info("material_libs表更新完成")
                
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"更新material_libs表失败: {e}")
            raise
    
    def estimate_import_time(self, csv_file_path: str) -> tuple:
        """
        预估导入时间
        """
        import os
        
        file_size_mb = os.path.getsize(csv_file_path) / 1024 / 1024
        
        # 基于经验值估算（每MB约需要0.5-2秒）
        min_time = file_size_mb * 0.5
        max_time = file_size_mb * 2
        
        self.logger.info(f"文件大小: {file_size_mb:.2f}MB")
        self.logger.info(f"预估导入时间: {min_time:.1f}-{max_time:.1f}秒")
        
        return min_time, max_time
    
    def optimize_database_for_import(self):
        """
        导入前数据库优化
        """
        self.logger.info("开始数据库导入优化...")
        
        try:
            with self.conn.cursor() as cur:
                # 保存当前设置
                cur.execute("SHOW work_mem;")
                original_work_mem = cur.fetchone()[0]
                
                cur.execute("SHOW maintenance_work_mem;")
                original_maintenance_work_mem = cur.fetchone()[0]
                
                cur.execute("SHOW synchronous_commit;")
                original_sync_commit = cur.fetchone()[0]
                
                # 存储原始设置
                self.original_settings = {
                    'work_mem': original_work_mem,
                    'maintenance_work_mem': original_maintenance_work_mem,
                    'synchronous_commit': original_sync_commit
                }
                
                # 应用优化设置（移除无法动态修改的参数）
                optimizations = [
                    "SET work_mem = '256MB';",
                    "SET maintenance_work_mem = '1GB';",
                    "SET synchronous_commit = off;"
                ]
                
                for opt in optimizations:
                    cur.execute(opt)
                    self.logger.info(f"应用优化: {opt}")
                
                self.conn.commit()
                self.logger.info("数据库导入优化完成")
                
        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")
            raise
    
    def restore_database_settings(self):
        """
        恢复数据库原始设置
        """
        if not hasattr(self, 'original_settings'):
            return
            
        self.logger.info("恢复数据库原始设置...")
        
        try:
            with self.conn.cursor() as cur:
                # 恢复原始设置
                cur.execute(f"SET work_mem = '{self.original_settings['work_mem']}';")
                cur.execute(f"SET maintenance_work_mem = '{self.original_settings['maintenance_work_mem']}';")
                cur.execute(f"SET synchronous_commit = {self.original_settings['synchronous_commit']};")
                
                self.conn.commit()
                self.logger.info("数据库设置已恢复")
                
        except Exception as e:
            self.logger.error(f"恢复数据库设置失败: {e}")
    
    def drop_indexes_for_import(self):
        """
        导入前删除索引以提高性能
        """
        self.logger.info("删除索引以优化导入性能...")
        
        try:
            with self.conn.cursor() as cur:
                # 查询现有索引
                cur.execute("""
                    SELECT indexname, tablename 
                    FROM pg_indexes 
                    WHERE tablename = 'material_items' 
                    AND indexname != 'material_items_pkey';
                """)
                
                indexes = cur.fetchall()
                self.dropped_indexes = []
                
                # 删除非主键索引
                for index_name, table_name in indexes:
                    try:
                        cur.execute(f"DROP INDEX IF EXISTS {index_name};")
                        self.dropped_indexes.append(index_name)
                        self.logger.info(f"删除索引: {index_name}")
                    except Exception as e:
                        self.logger.warning(f"删除索引 {index_name} 失败: {e}")
                
                self.conn.commit()
                self.logger.info(f"成功删除 {len(self.dropped_indexes)} 个索引")
                
        except Exception as e:
            self.logger.error(f"删除索引失败: {e}")
            raise
    
    def recreate_indexes_after_import(self):
        """
        导入后重建索引
        """
        if not hasattr(self, 'dropped_indexes') or not self.dropped_indexes:
            return
            
        self.logger.info("重建索引...")
        
        try:
            with self.conn.cursor() as cur:
                # 重建常用索引
                index_definitions = {
                    'idx_material_items_cas_no': 'CREATE INDEX CONCURRENTLY idx_material_items_cas_no ON material_items(cas_no);',
                    'idx_material_items_source': 'CREATE INDEX CONCURRENTLY idx_material_items_source ON material_items(source);',
                    'idx_material_items_material_lib_id': 'CREATE INDEX CONCURRENTLY idx_material_items_material_lib_id ON material_items(material_lib_id);',
                    'idx_material_items_canonical_smiles': 'CREATE INDEX CONCURRENTLY idx_material_items_canonical_smiles ON material_items(canonical_smiles);'
                }
                
                for index_name in self.dropped_indexes:
                    if index_name in index_definitions:
                        try:
                            cur.execute(index_definitions[index_name])
                            self.logger.info(f"重建索引: {index_name}")
                        except Exception as e:
                            self.logger.warning(f"重建索引 {index_name} 失败: {e}")
                
                self.conn.commit()
                self.logger.info("索引重建完成")
                
        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
    
    def update_material_items(self, csv_file_path: str):
        """
        更新material_items表数据 - 使用psql的\copy命令（优化版本）
        """
        import subprocess
        import os
        import time
        
        try:
            # 预估导入时间
            min_time, max_time = self.estimate_import_time(csv_file_path)
            
            # 应用数据库优化
            self.optimize_database_for_import()
            
            # 删除索引以提高导入性能
            self.drop_indexes_for_import()
            
            with self.conn.cursor() as cur:
                # 清空表
                cur.execute("TRUNCATE TABLE material_items RESTART IDENTITY CASCADE;")
                self.conn.commit()
                self.logger.info("已清空material_items表")
            
            # 构建psql命令
            db_config = self.config
            psql_cmd = [
                'psql',
                '-h', db_config['DB_HOST'],
                '-p', str(db_config['DB_PORT']),
                '-U', db_config['DB_USER'],
                '-d', db_config['DB_NAME'],
                '-c', f"\\copy material_items (id, material_lib_id, source, source_link, canonical_smiles, inchified_smiles, unified_smiles, cas_no, codes, pubchem_safety_link, name_en, name_zh, material_id, quantity, unit, price, unified_unit, unit_price, unit_quantity, lowest_unit_price, in_stock, max_delivery_days, min_delivery_days, purity, extension) FROM '{csv_file_path}' WITH CSV HEADER DELIMITER ','"
            ]
            
            # 设置环境变量以避免密码提示
            env = os.environ.copy()
            env['PGPASSWORD'] = db_config['DB_PASSWORD']
            
            # 执行psql命令并计时
            self.logger.info(f"开始使用psql \\copy命令导入数据: {csv_file_path}")
            start_time = time.time()
            
            result = subprocess.run(
                psql_cmd,
                env=env,
                capture_output=True,
                text=True,
                check=True
            )
            
            actual_time = time.time() - start_time
            
            # 解析导入结果
            if result.stdout:
                self.logger.info(f"psql输出: {result.stdout.strip()}")
            
            # 获取导入的记录数并重置序列
            with self.conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM material_items;")
                count = cur.fetchone()[0]
                self.logger.info(f"成功导入 {count} 条记录到material_items表")
                self.logger.info(f"实际导入时间: {actual_time:.1f}秒 (预估: {min_time:.1f}-{max_time:.1f}秒)")
                
                # 重置序列
                cur.execute("""
                    SELECT setval('material_items_id_seq', 
                                  (SELECT MAX(id) FROM material_items));
                """)
                
                self.conn.commit()
                self.logger.info("material_items表更新完成")
            
            # 重建索引
            self.recreate_indexes_after_import()
            
            # 恢复数据库设置
            self.restore_database_settings()
                
        except subprocess.CalledProcessError as e:
            self.logger.error(f"psql命令执行失败: {e.stderr}")
            # 确保在失败时也恢复设置
            self.restore_database_settings()
            raise
        except Exception as e:
            if hasattr(self, 'conn'):
                self.conn.rollback()
            self.logger.error(f"更新material_items表失败: {str(e)}")
            # 确保在失败时也恢复设置
            self.restore_database_settings()
            raise
    
    def update_material_links(self):
        """更新material_items_material_lib_links关联表"""
        self.logger.info("开始更新material_items_material_lib_links表...")
        
        try:
            with self.conn.cursor() as cur:
                # 清空关联表
                cur.execute("TRUNCATE TABLE material_items_material_lib_links RESTART IDENTITY;")
                
                # 建立关联关系
                cur.execute("""
                    INSERT INTO material_items_material_lib_links 
                    (material_item_id, material_lib_id, material_item_order)
                    SELECT 
                        mi.id as material_item_id,
                        ml.id as material_lib_id,
                        mi.id::float8 as material_item_order
                    FROM material_items mi
                    JOIN material_libs ml ON (
                        CASE 
                            WHEN mi.source = 'Leyan' THEN ml.name = 'Leyan'
                            WHEN mi.source = 'Macklin' THEN ml.name = 'Macklin'
                            WHEN mi.source = 'BidePharm' THEN ml.name = 'BidePharm'
                            WHEN mi.source = 'Aladdin' THEN ml.name = 'Aladdin'
                            WHEN mi.source = 'Merck' THEN ml.name = 'Merck'
                            WHEN mi.source = 'Rhawn' THEN ml.name = 'Rhawn'
                            WHEN mi.source = 'TCI' THEN ml.name = 'TCI'
                            WHEN mi.source = 'Reagent' THEN ml.name = 'Reagent'
                            ELSE FALSE
                        END
                    )
                    WHERE mi.source IS NOT NULL;
                """)
                
                # 更新material_items表中的material_lib_id字段
                cur.execute("""
                    UPDATE material_items 
                    SET material_lib_id = (
                        SELECT ml.id::varchar(255)
                        FROM material_libs ml
                        WHERE ml.name = material_items.source
                    )
                    WHERE source IS NOT NULL;
                """)
                
                self.conn.commit()
                self.logger.info("material_items_material_lib_links表更新完成")
                
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"更新关联表失败: {e}")
            raise
    
    def validate_migration(self):
        """验证迁移结果"""
        self.logger.info("开始验证迁移结果...")
        
        try:
            with self.conn.cursor() as cur:
                # 验证material_libs表
                cur.execute("SELECT COUNT(*) FROM material_libs;")
                libs_count = cur.fetchone()[0]
                self.logger.info(f"material_libs表记录数: {libs_count}")
                
                # 验证material_items表
                cur.execute("SELECT COUNT(*) FROM material_items;")
                items_count = cur.fetchone()[0]
                self.logger.info(f"material_items表记录数: {items_count}")
                
                # 验证关联表
                cur.execute("SELECT COUNT(*) FROM material_items_material_lib_links;")
                links_count = cur.fetchone()[0]
                self.logger.info(f"material_items_material_lib_links表记录数: {links_count}")
                
                # 验证数据分布
                cur.execute("""
                    SELECT 
                        ml.name as lib_name,
                        COUNT(link.material_item_id) as item_count,
                        ROUND(COUNT(link.material_item_id) * 100.0 / %s, 2) as percentage
                    FROM material_libs ml
                    LEFT JOIN material_items_material_lib_links link ON ml.id = link.material_lib_id
                    GROUP BY ml.id, ml.name
                    ORDER BY ml.id;
                """, (items_count,))
                
                distribution = cur.fetchall()
                self.logger.info("数据分布:")
                for lib_name, count, percentage in distribution:
                    self.logger.info(f"  {lib_name}: {count} 条记录 ({percentage}%)")
                
                # 检查未关联的items
                cur.execute("""
                    SELECT COUNT(*) as unlinked_items
                    FROM material_items mi
                    LEFT JOIN material_items_material_lib_links link ON mi.id = link.material_item_id
                    WHERE link.material_item_id IS NULL;
                """)
                
                unlinked_count = cur.fetchone()[0]
                if unlinked_count > 0:
                    self.logger.warning(f"发现 {unlinked_count} 条未关联的记录")
                else:
                    self.logger.info("所有记录都已正确关联")
                
        except Exception as e:
            self.logger.error(f"验证失败: {e}")
            raise
    
    def run_migration(self, csv_file_path: str):
        """执行完整的迁移流程"""
        self.logger.info("开始执行数据库迁移...")
        
        try:
            self.connect()
            
            # 步骤1: 备份数据
            self.backup_tables()
            
            # 步骤2: 更新material_libs表
            self.update_material_libs()
            
            # 步骤3: 更新material_items表
            self.update_material_items(csv_file_path)
            
            # 步骤4: 更新关联表
            self.update_material_links()
            
            # 步骤5: 验证结果
            self.validate_migration()
            
            self.logger.info("数据库迁移完成!")
            
        except Exception as e:
            self.logger.error(f"迁移失败: {e}")
            if self.conn:
                self.conn.rollback()
            raise
        finally:
            self.disconnect()


def setup_logging(log_level: str = 'INFO'):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def load_config(env_file: str) -> Dict[str, Any]:
    """加载配置"""
    load_dotenv(env_file)
    
    required_vars = [
        'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'
    ]
    
    optional_vars = [
        'BACKUP_DIR'
    ]
    
    config = {}
    
    # 加载必需的环境变量
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            raise ValueError(f"环境变量 {var} 未设置")
        config[var] = value
    
    # 加载可选的环境变量
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            config[var] = value
    
    # 转换端口为整数
    config['DB_PORT'] = int(config['DB_PORT'])
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PostgreSQL数据库迁移脚本')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('--env-file', default='.env', help='环境变量文件路径 (默认: .env)')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 检查CSV文件是否存在
        if not Path(args.csv_file).exists():
            raise FileNotFoundError(f"CSV文件不存在: {args.csv_file}")
        
        # 加载配置
        config = load_config(args.env_file)
        logger.info(f"配置加载成功，连接到数据库: {config['DB_HOST']}:{config['DB_PORT']}/{config['DB_NAME']}")
        
        # 执行迁移
        migrator = PostgreSQLMigrator(config)
        migrator.run_migration(args.csv_file)
        
        logger.info("迁移成功完成!")
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()