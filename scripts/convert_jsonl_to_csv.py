#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL到MaterialItem CSV转换脚本

根据field_mapping_report.md的映射规则，将JSONL数据转换为CSV格式
包含错误处理和警告记录功能

用法:
    python3 convert_jsonl_to_csv.py input.jsonl
    python3 convert_jsonl_to_csv.py input.jsonl output.csv error.log warning.log
    python3 convert_jsonl_to_csv.py input.jsonl --batch-size 5000 --verbose
"""

import sys
import os
import json
import csv
import tarfile
import re
import pandas as pd
import argparse
import logging
import logging.handlers
import time
import traceback
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

# Script metadata
SCRIPT_NAME = "convert_jsonl_to_csv"
VERSION = "1.0.0"
DEFAULT_BATCH_SIZE = 1000

# Custom exceptions
class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

@dataclass
class ProcessingResult:
    """Standardized result structure for processing operations"""
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float

@dataclass
class ScriptConfig:
    """Configuration class for script parameters"""
    batch_size: int = DEFAULT_BATCH_SIZE
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    verbose: bool = False
    dry_run: bool = False

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = SCRIPT_NAME
) -> logging.Logger:
    """Setup standardized logging configuration"""

    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)

        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)

        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)

    return logger

def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Convert JSONL files to CSV format with material data processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""Examples:
  python3 {SCRIPT_NAME}.py input.jsonl
  python3 {SCRIPT_NAME}.py input.jsonl output.csv error.log warning.log
  python3 {SCRIPT_NAME}.py input.jsonl --batch-size 5000 --verbose"""
    )

    # Required positional arguments (maintaining backward compatibility)
    parser.add_argument('input_file_path',
                       nargs='?',
                       default="/Users/<USER>/Work/material-data/data/origin/product_client_250609.jsonl.tar.gz",
                       help='Input JSONL file path')
    parser.add_argument('output_file_path',
                       nargs='?',
                       default="/Users/<USER>/Work/material-data/data/converted/material_items.csv",
                       help='Output CSV file path (optional)')
    parser.add_argument('error_file_path',
                       nargs='?',
                       default="/Users/<USER>/Work/material-data/data/converted/conversion_errors.log",
                       help='Error log file path (optional)')
    parser.add_argument('warning_file_path',
                       nargs='?',
                       default="/Users/<USER>/Work/material-data/data/converted/conversion_warnings.log",
                       help='Warning log file path (optional)')

    # Common optional arguments
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')

    return parser


class JSONLToCSVConverter:
    def __init__(self):
        # 供应商映射（按占比排序）
        self.vendor_mapping = {
            "Leyan": 1,
            "Macklin": 2,
            "BidePharm": 3,
            "Aladdin": 4,
            "Merck": 5,
            "Rhawn": 6,
            "TCI": 7,
            "Reagent": 8,
        }

        # 单位转换映射 (转换为基本单位)
        self.unit_conversions = {
            "kg": ("g", 1000),
            "mg": ("g", 0.001),
            "ug": ("g", 0.000001),
            "μg": ("g", 0.000001),
            "t": ("g", 1000000),
            "L": ("ml", 1000),
            "l": ("ml", 1000),
            "mL": ("ml", 1),
            "μL": ("ml", 0.001),
            "μl": ("ml", 0.001),
            "uL": ("ml", 0.001),
            "ul": ("ml", 0.001),
            "mmol": ("mol", 0.001),
            "μmol": ("mol", 0.000001),
            "umol": ("mol", 0.000001),
        }

        # 统一单位映射
        self.unified_unit_mapping = {
            "mg": "g",
            "g": "g",
            "kg": "g",
            "t": "g",  # 吨统一为g
            "μg": "g",  # 微克统一为g
            "ug": "g",  # 微克统一为g
            "ml": "ml",
            "l": "ml",
            "L": "ml",  # 添加L到ml的映射
            "mL": "ml",
            "μL": "ml",
            "μl": "ml",
            "uL": "ml",
            "ul": "ml",
            "mol": "mol",  # mol作为基础单位
            "mmol": "mol",
            "μmol": "mol",
            "umol": "mol",
        }

        self.errors = []
        self.warnings = []
        self.unknown_units = set()  # 收集未知单位
        self.error_stats = {}  # 错误统计
        self.warning_stats = {}  # 警告统计
        self.parsed_specifications_success = set()  # 成功解析的specification
        self.parsed_specifications_failed = set()  # 解析失败的specification
        self.parsed_specifications_failed_count = {}  # 解析失败的specification及其次数
        self.total_processed_lines = 0  # 实际处理的总行数

    def validate_cas(self, cas: str) -> bool:
        """验证CAS号格式"""
        if not cas:
            return False
        # CAS号格式：数字-数字-数字
        pattern = r"^\d{2,7}-\d{2}-\d$"
        return bool(re.match(pattern, cas))

    def get_unified_unit(self, unit: str) -> Tuple[str, bool]:
        """获取统一单位，返回(统一单位, 是否有警告)"""
        if unit in self.unified_unit_mapping:
            return self.unified_unit_mapping[unit], False
        else:
            # 收集未知单位，但不立即产生警告
            self.unknown_units.add(unit)
            return unit, False

    def convert_unit_value(self, value: float, from_unit: str, to_unit: str) -> float:
        """转换单位值"""
        if from_unit == to_unit:
            return value

        if from_unit in self.unit_conversions and to_unit in self.unit_conversions:
            # 先转换为基础单位，再转换为目标单位
            from_base_unit, from_factor = self.unit_conversions[from_unit]
            to_base_unit, to_factor = self.unit_conversions[to_unit]

            if from_base_unit == to_base_unit:
                base_value = value * from_factor
                return base_value / to_factor

        return value

    def convert_unit_and_calculate(
        self, quantity: float, unit: str
    ) -> Tuple[float, float, str]:
        """转换单位并计算单价和单数量"""
        # 获取统一单位
        unified_unit = self.unified_unit_mapping.get(unit, unit)

        # 转换数量到统一单位
        if unit in self.unit_conversions:
            target_unit, conversion_factor = self.unit_conversions[unit]
            converted_quantity = quantity * conversion_factor
        else:
            converted_quantity = quantity

        return converted_quantity, quantity, unified_unit

    def add_error_stat(self, error_msg: str):
        """统计错误类型"""
        if error_msg in self.error_stats:
            self.error_stats[error_msg] += 1
        else:
            self.error_stats[error_msg] = 1

    def add_warning_stat(self, warning_msg: str):
        """统计警告类型"""
        if warning_msg in self.warning_stats:
            self.warning_stats[warning_msg] += 1
        else:
            self.warning_stats[warning_msg] = 1

    def parse_specification(
        self, specification: str
    ) -> Tuple[Optional[float], Optional[str]]:
        """从specification字段解析数量和单位，返回(数量, 单位)"""
        if not specification or not isinstance(specification, str):
            return None, None

        import re
        import html

        # HTML实体解码
        spec = html.unescape(specification.strip())

        # 模式1: 基本数字+单位格式 (如: 100mg, 0.5KU)
        pattern1 = r"^([0-9]*\.?[0-9]+(?:[eE][+-]?[0-9]+)?)\s*([a-zA-Zμ]+)$"
        match = re.match(pattern1, spec)
        if match:
            try:
                quantity = float(match.group(1))
                unit = match.group(2)
                return quantity, unit
            except ValueError:
                pass

        # 模式2: 产品编号格式 (如: 00017-25MG, W398500-25KG-K)
        pattern2 = r"\w+-([0-9]*\.?[0-9]+)([A-Za-z]+)(?:-\w+)?$"
        match = re.match(pattern2, spec)
        if match:
            try:
                quantity = float(match.group(1))
                unit = match.group(2)
                return quantity, unit
            except ValueError:
                pass

        # 模式3: 乘法格式 (如: 10×0.5mL)
        pattern3 = r"^([0-9]+)×([0-9]*\.?[0-9]+)([A-Za-z]+)$"
        match = re.match(pattern3, spec)
        if match:
            try:
                multiplier = float(match.group(1))
                base_quantity = float(match.group(2))
                unit = match.group(3)
                # 返回总量
                return multiplier * base_quantity, unit
            except ValueError:
                pass

        # 模式4: 分隔符格式 (如: 10kg/桶, 10mL×2/盒)
        pattern4 = r"^([0-9]*\.?[0-9]+)([A-Za-z]+)/\w+$"
        match = re.match(pattern4, spec)
        if match:
            try:
                quantity = float(match.group(1))
                unit = match.group(2)
                return quantity, unit
            except ValueError:
                pass

        # 模式5: 中文单位格式 (如: 1000g, 2.5千克)
        chinese_units = {
            "千克": "kg",
            "公斤": "kg",
            "克": "g",
            "毫克": "mg",
            "升": "L",
            "毫升": "ml",
        }
        for cn_unit, en_unit in chinese_units.items():
            pattern5 = f"^([0-9]*\.?[0-9]+){cn_unit}$"
            match = re.match(pattern5, spec)
            if match:
                try:
                    quantity = float(match.group(1))
                    return quantity, en_unit
                except ValueError:
                    pass

        # 模式6: 复合液体格式 (如: A液100ml+B液100ml)
        pattern6 = (
            r"\w+液([0-9]*\.?[0-9]+)([A-Za-z]+)\+\w+液([0-9]*\.?[0-9]+)([A-Za-z]+)"
        )
        match = re.search(pattern6, spec)
        if match:
            try:
                qty1 = float(match.group(1))
                unit1 = match.group(2)
                qty2 = float(match.group(3))
                unit2 = match.group(4)
                # 如果单位相同，返回总量
                if unit1.lower() == unit2.lower():
                    return qty1 + qty2, unit1
            except ValueError:
                pass

        return None, None

    def process_row(
        self, row_data: Dict[str, Any], line_num: int
    ) -> Tuple[Optional[Dict[str, Any]], List[str], List[str]]:
        """处理单行数据，返回(转换后的数据, 错误列表, 警告列表)"""
        errors = []
        warnings = []
        result = {}

        try:
            # 处理 spec_quantity 和 spec_unit
            spec_quantity = row_data.get("spec_quantity")
            spec_unit = row_data.get("spec_unit")
            specification = row_data.get("specification", "")

            # 如果spec_quantity或spec_unit为空，尝试从specification解析
            if (not spec_quantity or not spec_unit) and specification:
                parsed_quantity, parsed_unit = self.parse_specification(specification)
                if parsed_quantity is not None and parsed_unit is not None:
                    # 解析成功，使用解析的值
                    if not spec_quantity:
                        spec_quantity = parsed_quantity
                    if not spec_unit:
                        spec_unit = parsed_unit
                    self.parsed_specifications_success.add(specification)
                else:
                    # 解析失败，记录specification和次数
                    self.parsed_specifications_failed.add(specification)
                    if specification in self.parsed_specifications_failed_count:
                        self.parsed_specifications_failed_count[specification] += 1
                    else:
                        self.parsed_specifications_failed_count[specification] = 1

            # 必填字段检查
            required_fields = {
                "price": "price",
                "sku_id": "material_id",
                "vendor": "source",
                "cano_smiles": "canonical_smiles",
            }

            for jsonl_field, csv_field in required_fields.items():
                if jsonl_field not in row_data or not row_data[jsonl_field]:
                    errors.append(
                        f"{csv_field}字段缺失或为空 (JSONL字段: {jsonl_field})"
                    )
                    continue

                if jsonl_field == "cano_smiles":
                    # canonical_smiles取数组第一个值
                    smiles_list = row_data[jsonl_field]
                    if isinstance(smiles_list, list) and len(smiles_list) > 0:
                        # 检查数组第一个元素是否为空
                        first_smiles = smiles_list[0]
                        if first_smiles and first_smiles.strip():
                            result[csv_field] = first_smiles.strip()
                        else:
                            errors.append(f"canonical_smiles数组第一个元素为空")
                    else:
                        errors.append(f"canonical_smiles数组为空或格式错误")
                elif jsonl_field == "price":
                    # 价格验证
                    try:
                        price = float(row_data[jsonl_field])
                        if price <= 0:
                            errors.append(f"价格必须大于0")
                        else:
                            result[csv_field] = price
                    except (ValueError, TypeError):
                        errors.append(f"价格格式错误: {row_data[jsonl_field]}")
                else:
                    result[csv_field] = row_data[jsonl_field]

            # 初始化默认值，防止KeyError
            result["quantity"] = 0
            result["unit"] = ""

            # 处理spec_quantity和spec_unit
            # 检查所有相关字段是否都为空
            if not spec_quantity and not spec_unit and not specification:
                errors.append(f"数量、单位和规格字段均缺失")
            elif spec_quantity and spec_unit:
                # 数量验证
                try:
                    quantity = float(spec_quantity)
                    if quantity <= 0:
                        errors.append(f"数量必须大于0")
                    else:
                        result["quantity"] = quantity
                        result["unit"] = spec_unit
                except (ValueError, TypeError):
                    errors.append(f"数量格式错误: {spec_quantity}")
            elif spec_quantity or spec_unit:
                # 如果只有一个字段有值，尝试使用已有的值
                if spec_quantity:
                    try:
                        quantity = float(spec_quantity)
                        if quantity <= 0:
                            errors.append(f"数量必须大于0")
                        else:
                            result["quantity"] = quantity
                            result["unit"] = spec_unit if spec_unit else ""
                    except (ValueError, TypeError):
                        errors.append(f"数量格式错误: {spec_quantity}")
                else:
                    # 只有spec_unit有值，但没有数量
                    errors.append(f"spec_quantity字段缺失但spec_unit存在")
            elif (
                (not spec_quantity and not spec_unit)
                and specification
                and specification not in self.parsed_specifications_success
            ):
                # 只有当spec_quantity和spec_unit都为空，且specification解析失败时才报错
                errors.append(
                    f"spec_quantity和spec_unit字段均缺失且specification解析失败"
                )

            # 如果有必填字段错误，直接返回
            if errors:
                return None, errors, warnings

            # material_lib_id处理 - 使用已验证的vendor值
            vendor = result["source"]  # 使用已经通过必填字段检查的vendor值
            if vendor in self.vendor_mapping:
                result["material_lib_id"] = self.vendor_mapping[vendor]
            else:
                errors.append(f"未知供应商: {vendor}")
                return None, errors, warnings

            # CAS号处理 - 可选字段，允许为空
            cas = row_data.get("cas", "")
            if cas and isinstance(cas, str) and cas.strip():
                cas_cleaned = cas.strip()
                if self.validate_cas(cas_cleaned):
                    result["cas_no"] = cas_cleaned
                else:
                    warning_msg = "CAS号格式不正确"
                    warnings.append(f"{warning_msg}: {cas_cleaned}")
                    self.add_warning_stat(warning_msg)
                    result["cas_no"] = ""
            else:
                result["cas_no"] = ""

            # 直接映射字段 - 这些都是可选字段，允许为空
            name_zh = row_data.get("name_zh", "")
            result["name_zh"] = (
                name_zh.replace("\n", " ").replace("\r", "").strip()
                if name_zh and isinstance(name_zh, str)
                else ""
            )

            result["in_stock"] = bool(row_data.get("in_stock", False))

            source_link = row_data.get("product_url", "")
            result["source_link"] = (
                source_link.replace("\n", " ").replace("\r", "").strip()
                if source_link and isinstance(source_link, str)
                else ""
            )

            # name_en从iupac_name数组取第一个值
            iupac_name = row_data.get("iupac_name", [])
            if isinstance(iupac_name, list) and len(iupac_name) > 0:
                # 检查数组第一个元素是否为有效字符串
                first_name = iupac_name[0]
                if first_name and isinstance(first_name, str) and first_name.strip():
                    result["name_en"] = (
                        first_name.replace("\n", " ").replace("\r", "").strip()
                    )
                else:
                    result["name_en"] = ""
            else:
                result["name_en"] = ""

            # extension字段
            specification = row_data.get("specification", "")
            specification = (
                specification.replace("\n", " ").replace("\r", "")
                if isinstance(specification, str)
                else specification
            )

            stock_detail_raw = row_data.get("stock_detail", [])
            stock_detail = (
                [
                    s.replace("\n", " ").replace("\r", "") if isinstance(s, str) else s
                    for s in stock_detail_raw
                ]
                if isinstance(stock_detail_raw, list)
                else stock_detail_raw
            )

            extension = {
                "specification": specification,
                "inchikey": row_data.get("inchikey", []),
                "stock_detail": stock_detail,
            }
            result["extension"] = json.dumps(extension, ensure_ascii=False)

            # unified_unit处理
            unit = result["unit"]
            unified_unit, _ = self.get_unified_unit(unit)
            result["unified_unit"] = unified_unit

            # unit_price和unit_quantity计算
            price = result["price"]
            quantity = result["quantity"]

            # 计算unit_quantity (转换到统一单位) - 使用更高精度避免四舍五入为0
            if unit in self.unit_conversions:
                target_unit, conversion_factor = self.unit_conversions[unit]
                if target_unit == unified_unit:
                    converted_value = quantity * conversion_factor
                    # 使用10位小数精度，确保μg级别的数量不会被四舍五入为0
                    result["unit_quantity"] = round(converted_value, 10)
                else:
                    result["unit_quantity"] = round(quantity, 10)
            else:
                result["unit_quantity"] = round(quantity, 10)

            # 检查unit_quantity是否为0或极小值（小于1e-10视为0）
            if result["unit_quantity"] == 0 or result["unit_quantity"] < 1e-10:
                warnings.append(
                    f"unit_quantity为0或极小值({result['unit_quantity']})，尝试从specification重新解析"
                )
                # 尝试从specification重新解析
                specification = row_data.get("specification", "")
                if specification:
                    parsed_quantity, parsed_unit = self.parse_specification(
                        specification
                    )
                    if (
                        parsed_quantity is not None
                        and parsed_unit is not None
                        and parsed_quantity > 0
                    ):
                        result["quantity"] = parsed_quantity
                        result["unit"] = parsed_unit
                        # 重新计算unit_quantity
                        if parsed_unit in self.unit_conversions:
                            target_unit, conversion_factor = self.unit_conversions[
                                parsed_unit
                            ]
                            if target_unit == unified_unit:
                                converted_value = parsed_quantity * conversion_factor
                                result["unit_quantity"] = round(converted_value, 10)
                            else:
                                result["unit_quantity"] = round(parsed_quantity, 10)
                        else:
                            result["unit_quantity"] = round(parsed_quantity, 10)

                        # 如果解析后仍为0或极小值，记录错误
                        if (
                            result["unit_quantity"] == 0
                            or result["unit_quantity"] < 1e-10
                        ):
                            errors.append(
                                f"从specification解析的数量仍为0或极小值: {specification}"
                            )
                            return None, errors, warnings
                    else:
                        errors.append(
                            f"unit_quantity为0且无法从specification解析有效数量: {specification}"
                        )
                        return None, errors, warnings
                else:
                    errors.append(f"unit_quantity为0且specification字段为空")
                    return None, errors, warnings

            # 计算unit_price (基于统一单位的单价)
            if result["unit_quantity"] > 1e-10:
                result["unit_price"] = round(price / result["unit_quantity"], 2)
            else:
                errors.append(
                    f"unit_quantity为0或极小值({result['unit_quantity']})，无法计算unit_price"
                )
                return None, errors, warnings

            # 设置默认值的字段
            result["id"] = ""
            result["inchified_smiles"] = ""
            result["codes"] = "[]"
            result["pubchem_safety_link"] = ""
            result["lowest_unit_price"] = False
            result["min_delivery_days"] = ""
            result["max_delivery_days"] = ""
            result["purity"] = ""
            result["unified_smiles"] = ""

            return result, errors, warnings

        except Exception as e:
            errors.append(f"处理行数据时发生异常: {str(e)}")
            return None, errors, warnings

    def convert_file(
        self, input_file: str, output_csv: str, error_log: str, warning_log: str
    ):
        """转换文件"""
        print(f"开始转换文件: {input_file}")
        print(f"输出CSV: {output_csv}")
        print(f"错误日志: {error_log}")
        print(f"警告日志: {warning_log}")

        # CSV字段顺序
        csv_fields = [
            "id",
            "material_lib_id",
            "source",
            "source_link",
            "canonical_smiles",
            "inchified_smiles",
            "unified_smiles",
            "cas_no",
            "codes",
            "pubchem_safety_link",
            "name_en",
            "name_zh",
            "material_id",
            "quantity",
            "unit",
            "price",
            "unified_unit",
            "unit_price",
            "unit_quantity",
            "lowest_unit_price",
            "in_stock",
            "max_delivery_days",
            "min_delivery_days",
            "purity",
            "extension",
        ]

        processed_count = 0
        error_count = 0
        warning_count = 0

        with (
            open(output_csv, "w", newline="", encoding="utf-8") as csvfile,
            open(error_log, "w", encoding="utf-8") as errorfile,
            open(warning_log, "w", encoding="utf-8") as warningfile,
        ):
            writer = csv.DictWriter(csvfile, fieldnames=csv_fields)
            writer.writeheader()

            # 写入日志文件头部
            errorfile.write(f"错误日志 - 生成时间: {datetime.now()}\n")
            errorfile.write("=" * 80 + "\n\n")

            warningfile.write(f"警告日志 - 生成时间: {datetime.now()}\n")
            warningfile.write("=" * 80 + "\n\n")

            # 根据文件扩展名确定处理方式
            line_num = 0

            def process_jsonl_lines(file_obj, batch_size=None):
                """批量处理JSONL文件的行"""
                nonlocal processed_count, error_count, warning_count, line_num

                # 动态确定批次大小
                if batch_size is None:
                    try:
                        # 尝试获取文件大小来确定最优批次大小
                        if hasattr(file_obj, "name"):
                            file_size = (
                                os.path.getsize(file_obj.name) / 1024 / 1024
                            )  # MB
                        else:
                            file_size = 10  # 默认假设为中等文件

                        if file_size < 10:
                            batch_size = 500
                        elif file_size < 100:
                            batch_size = 1000
                        else:
                            batch_size = 2000

                        print(
                            f"📊 文件大小: {file_size:.2f} MB, 自动选择批次大小: {batch_size}"
                        )
                    except:
                        batch_size = 1000  # 默认批次大小
                        print(f"📊 使用默认批次大小: {batch_size}")

                batch_lines = []
                batch_line_nums = []

                def process_batch():
                    """处理当前批次"""
                    nonlocal processed_count, error_count, warning_count

                    if not batch_lines:
                        return

                    # 批量解析JSON
                    batch_data = []
                    batch_errors = []
                    batch_warnings = []

                    for i, (line, current_line_num) in enumerate(
                        zip(batch_lines, batch_line_nums)
                    ):
                        try:
                            # 解析JSON
                            if isinstance(line, bytes):
                                line_str = line.decode("utf-8").strip()
                            else:
                                line_str = line.strip()

                            if not line_str:
                                continue

                            row_data = json.loads(line_str)
                            batch_data.append((row_data, current_line_num, line_str))

                        except json.JSONDecodeError as e:
                            error_count += 1
                            self.add_error_stat("JSON解析错误")
                            batch_errors.append(
                                (
                                    current_line_num,
                                    f"JSON解析错误: {str(e)}",
                                    line_str[:200]
                                    if "line_str" in locals()
                                    else str(line)[:200],
                                )
                            )
                        except Exception as e:
                            error_count += 1
                            self.add_error_stat("未知错误")
                            batch_errors.append(
                                (
                                    current_line_num,
                                    f"处理异常: {str(e)}",
                                    line_str[:200]
                                    if "line_str" in locals()
                                    else str(line)[:200],
                                )
                            )

                    # 批量处理数据
                    batch_results = []
                    for row_data, current_line_num, line_str in batch_data:
                        try:
                            result, errors, warnings = self.process_row(
                                row_data, current_line_num
                            )

                            if errors:
                                error_count += len(errors)
                                for error in errors:
                                    self.add_error_stat(
                                        error.split(":")[0] if ":" in error else error
                                    )
                                batch_errors.append(
                                    (current_line_num, errors, line_str[:200])
                                )

                            if warnings:
                                warning_count += len(warnings)
                                for warning in warnings:
                                    self.add_warning_stat(
                                        warning.split(":")[0] if ":" in warning else warning
                                    )
                                batch_warnings.append(
                                    (current_line_num, warnings, line_str[:200])
                                )

                            if result:
                                batch_results.append(result)
                                processed_count += 1

                        except Exception as e:
                            error_count += 1
                            self.add_error_stat("未知错误")
                            batch_errors.append(
                                (
                                    current_line_num,
                                    f"处理异常: {str(e)}",
                                    line_str[:200],
                                )
                            )

                    # 批量写入CSV
                    if batch_results:
                        writer.writerows(batch_results)

                    # 批量写入错误日志
                    for current_line_num, errors, line_data in batch_errors:
                        if isinstance(errors, list):
                            errorfile.write(f"第 {current_line_num} 行错误:\n")
                            for error in errors:
                                errorfile.write(f"  - {error}\n")
                        else:
                            errorfile.write(f"第 {current_line_num} 行{errors}\n")
                        errorfile.write(f"  原始数据: {line_data}...\n\n")

                    # 批量写入警告日志
                    for current_line_num, warnings, line_data in batch_warnings:
                        warningfile.write(f"第 {current_line_num} 行警告:\n")
                        for warning in warnings:
                            warningfile.write(f"  - {warning}\n")
                        warningfile.write(f"  原始数据: {line_data}...\n\n")

                    # 清空批次数据
                    batch_lines.clear()
                    batch_line_nums.clear()

                # 逐行读取并批量处理
                for line in file_obj:
                    line_num += 1

                    if line_num % 10000 == 0:
                        print(f"⏳ 已处理 {line_num:,} 行...")

                    batch_lines.append(line)
                    batch_line_nums.append(line_num)

                    # 达到批次大小时处理
                    if len(batch_lines) >= batch_size:
                        process_batch()

                # 处理最后一批
                if batch_lines:
                    process_batch()

            # 根据文件扩展名选择处理方式
            if input_file.endswith(".tar.gz"):
                # 处理tar.gz文件
                with tarfile.open(input_file, "r:gz") as tar:
                    # 找到JSONL文件
                    jsonl_file = None
                    for member in tar.getmembers():
                        if (
                            member.isfile()
                            and member.name.endswith(".jsonl")
                            and not member.name.startswith("._")
                        ):
                            jsonl_file = member
                            break

                    if not jsonl_file:
                        print("❌ 未找到有效的JSONL文件")
                        return

                    print(f"📄 找到JSONL文件: {jsonl_file.name}")

                    with tar.extractfile(jsonl_file) as f:
                        process_jsonl_lines(f)

            elif input_file.endswith(".jsonl.gz"):
                # 处理gzip压缩的JSONL文件
                import gzip

                with gzip.open(input_file, "rt", encoding="utf-8") as f:
                    process_jsonl_lines(f)

            elif input_file.endswith(".jsonl"):
                # 处理普通JSONL文件
                with open(input_file, "r", encoding="utf-8") as f:
                    process_jsonl_lines(f)

            else:
                print(f"❌ 不支持的文件格式: {input_file}")
                print("支持的格式: .jsonl, .jsonl.gz, .jsonl.tar.gz")
                return

        # 更新总处理行数
        self.total_processed_lines = line_num
        
        # 生成统计报告
        stats_report = []
        stats_report.append(f"\n✅ 转换完成!")
        stats_report.append(f"📊 统计信息:")
        stats_report.append(f"  - 总处理行数: {line_num:,}")
        stats_report.append(f"  - 成功转换: {processed_count:,}")
        stats_report.append(f"  - 错误行数: {error_count:,}")
        stats_report.append(f"  - 警告行数: {warning_count:,}")
        stats_report.append(f"  - 成功率: {processed_count / line_num * 100:.2f}%")

        # 错误统计
        if self.error_stats:
            stats_report.append(f"\n❌ 错误类型统计:")
            for error_type, count in sorted(
                self.error_stats.items(), key=lambda x: x[1], reverse=True
            ):
                stats_report.append(f"  - {error_type}: {count:,} 次")

        # 警告统计
        if self.warning_stats:
            stats_report.append(f"\n⚠️ 警告类型统计:")
            for warning_type, count in sorted(
                self.warning_stats.items(), key=lambda x: x[1], reverse=True
            ):
                stats_report.append(f"  - {warning_type}: {count:,} 次")

        # 未知单位
        if self.unknown_units:
            stats_report.append(f"\n🔍 发现的未知单位:")
            for unit in sorted(self.unknown_units):
                stats_report.append(f"  - {unit}")
            stats_report.append(
                f"\n💡 提示: 发现 {len(self.unknown_units)} 种未知单位，这些单位保持原样未进行转换。"
            )

        # specification解析统计
        if self.parsed_specifications_success:
            stats_report.append(
                f"\n✅ 成功解析的specification ({len(self.parsed_specifications_success)} 个):"
            )
            for spec in sorted(self.parsed_specifications_success):
                stats_report.append(f"  - {spec}")

        if self.parsed_specifications_failed:
            # 计算解析失败的总次数
            total_failed_count = sum(self.parsed_specifications_failed_count.values())
            stats_report.append(f"\n❌ 解析失败的specification统计:")
            stats_report.append(
                f"  - 解析失败的specification种类数: {len(self.parsed_specifications_failed)} 个"
            )
            stats_report.append(f"  - 解析失败的总次数: {total_failed_count:,} 次")
            stats_report.append(
                f"  - 平均每个specification失败次数: {total_failed_count / len(self.parsed_specifications_failed):.1f} 次"
            )
            stats_report.append(
                f"\n❌ 解析失败的specification详情 ({len(self.parsed_specifications_failed)} 个):"
            )
            # 按失败次数排序显示
            sorted_failed = sorted(
                self.parsed_specifications_failed_count.items(),
                key=lambda x: x[1],
                reverse=True,
            )
            for spec, count in sorted_failed:
                if len(spec) > 80:
                    stats_report.append(f"  - {spec[:80]}... (失败 {count:,} 次)")
                else:
                    stats_report.append(f"  - {spec} (失败 {count:,} 次)")

        # 输出到控制台
        for line in stats_report:
            print(line)

        # 写入统计信息到日志文件
        stats_log = error_log.replace("conversion_errors.log", "conversion_stats.log")
        with open(stats_log, "w", encoding="utf-8") as statsfile:
            statsfile.write(f"转换统计报告 - 生成时间: {datetime.now()}\n")
            statsfile.write("=" * 80 + "\n")
            for line in stats_report:
                statsfile.write(line + "\n")

        print(f"\n📄 统计报告已保存到: {stats_log}")

    def convert_jsonl_to_dataframe(
        self, jsonl_file: str, temp_dir: str = None
    ) -> Tuple[pd.DataFrame, List[Dict], List[Dict]]:
        """将JSONL文件转换为DataFrame

        使用混合流式处理方案：先通过convert_file方法生成临时CSV，再读取为DataFrame
        这样可以避免内存中存储大量数据，提高处理大文件的性能

        Args:
            jsonl_file: 输入的JSONL文件路径
            temp_dir: 临时目录路径，如果为None则使用系统临时目录

        Returns:
            Tuple[pd.DataFrame, List[Dict], List[Dict]]: (成功数据DataFrame, 错误记录列表, 警告记录列表)
        """
        import tempfile
        import os

        logger = logging.getLogger(__name__)
        logger.info(f"开始转换JSONL文件: {jsonl_file}")

        # 创建临时文件
        if temp_dir is None:
            temp_dir = tempfile.mkdtemp()
            cleanup_temp_dir = True
        else:
            # 使用指定的临时目录
            os.makedirs(temp_dir, exist_ok=True)
            cleanup_temp_dir = False
            
        temp_csv = os.path.join(temp_dir, "temp_data.csv")
        temp_error_log = os.path.join(temp_dir, "temp_errors.log")
        temp_warning_log = os.path.join(temp_dir, "temp_warnings.log")
        
        logger.info(f"[子模块输出] 输出CSV: {temp_csv}")

        try:
            # 使用现有的convert_file方法进行流式处理
            logger.info("使用流式处理方式转换为临时CSV文件...")
            self.convert_file(jsonl_file, temp_csv, temp_error_log, temp_warning_log)

            # 读取CSV文件为DataFrame
            logger.info("读取临时CSV文件为DataFrame...")
            if os.path.exists(temp_csv) and os.path.getsize(temp_csv) > 0:
                # 分块读取CSV以节省内存
                chunk_size = 10000  # 每次读取10000行
                chunks = []

                for chunk in pd.read_csv(
                    temp_csv, chunksize=chunk_size, encoding="utf-8"
                ):
                    chunks.append(chunk)
                    if len(chunks) % 10 == 0:  # 每读取10个chunk报告一次进度
                        logger.info(
                            f"已读取 {len(chunks) * chunk_size:,} 行到DataFrame"
                        )

                if chunks:
                    df = pd.concat(chunks, ignore_index=True)
                    logger.info(f"成功创建DataFrame，共 {len(df):,} 行")
                else:
                    df = pd.DataFrame()
            else:
                df = pd.DataFrame()
                logger.warning("临时CSV文件为空或不存在")

            # 解析错误和警告记录
            error_records = self._parse_log_file(temp_error_log, "error")
            warning_records = self._parse_log_file(temp_warning_log, "warning")

            logger.info(
                f"转换完成: 成功 {len(df)} 行, 错误 {len(error_records)} 条, 警告 {len(warning_records)} 条"
            )

            return df, error_records, warning_records

        except Exception as e:
            logger.error(f"转换过程中发生错误: {str(e)}")
            raise
        finally:
            # 清理临时文件（仅当使用系统临时目录时）
            if cleanup_temp_dir:
                try:
                    import shutil

                    shutil.rmtree(temp_dir)
                    logger.debug(f"已清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录失败: {str(e)}")
            else:
                logger.debug(f"保留指定的临时目录: {temp_dir}")

    def _parse_log_file(self, log_file: str, log_type: str) -> List[Dict]:
        """解析日志文件，提取错误或警告记录"""
        records = []

        if not os.path.exists(log_file) or os.path.getsize(log_file) == 0:
            return records

        try:
            with open(log_file, "r", encoding="utf-8") as f:
                content = f.read()

            # 简单解析日志内容
            lines = content.split("\n")
            current_record = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检测新记录开始（包含"第 X 行"）
                if (
                    "第 " in line
                    and "行" in line
                    and (log_type in line or "错误" in line or "警告" in line)
                ):
                    if current_record:
                        records.append(current_record)

                    # 提取行号
                    try:
                        line_num_str = line.split("第 ")[1].split(" 行")[0]
                        line_num = int(line_num_str)
                    except (IndexError, ValueError):
                        line_num = 0

                    current_record = {
                        "line_number": line_num,
                        "raw_data": "",
                        log_type + "s": [],
                    }
                elif current_record and line.startswith("- "):
                    # 错误或警告消息
                    current_record[log_type + "s"].append(line[2:])
                elif current_record and line.startswith("原始数据:"):
                    # 原始数据
                    current_record["raw_data"] = line[5:]

            # 添加最后一条记录
            if current_record:
                records.append(current_record)

        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"解析日志文件失败 {log_file}: {str(e)}")

        return records

    def process_dataframe(
        self, df: pd.DataFrame
    ) -> Tuple[pd.DataFrame, List[Dict], List[Dict]]:
        """处理DataFrame数据

        Args:
            df: 输入的DataFrame，包含JSONL格式的数据

        Returns:
            Tuple[pd.DataFrame, List[Dict], List[Dict]]: (成功数据DataFrame, 错误记录列表, 警告记录列表)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"开始处理DataFrame，共 {len(df)} 行")

        success_data = []
        error_records = []
        warning_records = []

        for idx, row in df.iterrows():
            try:
                row_data = row.to_dict()
                result, errors, warnings = self.process_row(row_data, idx + 1)

                if result:
                    success_data.append(result)
                else:
                    error_records.append(
                        {"row_index": idx, "raw_data": row_data, "errors": errors}
                    )

                # 处理警告
                if warnings:
                    warning_records.append(
                        {"row_index": idx, "raw_data": row_data, "warnings": warnings}
                    )

            except Exception as e:
                error_records.append(
                    {
                        "row_index": idx,
                        "raw_data": row.to_dict(),
                        "errors": [f"处理行数据时发生异常: {str(e)}"],
                    }
                )

            # 进度报告
            if (idx + 1) % 10000 == 0:
                logger.info(f"已处理 {idx + 1} 行")

        # 创建DataFrame
        if success_data:
            result_df = pd.DataFrame(success_data)
            # 确保列顺序
            csv_fields = [
                "id",
                "material_lib_id",
                "source",
                "source_link",
                "canonical_smiles",
                "inchified_smiles",
                "unified_smiles",
                "cas_no",
                "codes",
                "pubchem_safety_link",
                "name_en",
                "name_zh",
                "material_id",
                "quantity",
                "unit",
                "price",
                "unified_unit",
                "unit_price",
                "unit_quantity",
                "lowest_unit_price",
                "in_stock",
                "max_delivery_days",
                "min_delivery_days",
                "purity",
                "extension",
            ]
            result_df = result_df.reindex(columns=csv_fields)
        else:
            result_df = pd.DataFrame()

        logger.info(
            f"处理完成: 成功 {len(success_data)} 行, 错误 {len(error_records)} 行, 警告 {len(warning_records)} 条"
        )

        return result_df, error_records, warning_records

    def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """
        Standardized interface for DataFrame processing (for compatibility with other updaters)

        Args:
            df: Input DataFrame to process

        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (processed_df, error_records)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"Processing DataFrame with {len(df)} rows using standardized interface")

        # Use the existing process_dataframe method
        success_df, error_records, warning_records = self.process_dataframe(df)

        # Combine error and warning records for the standardized interface
        all_error_records = []
        all_error_records.extend([{**record, 'record_type': 'error'} for record in error_records])
        all_error_records.extend([{**record, 'record_type': 'warning'} for record in warning_records])

        logger.info(f"Processing complete: {len(success_df)} success, {len(all_error_records)} errors/warnings")

        return success_df, all_error_records

    def process_csv(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """
        Standardized interface for CSV processing

        Args:
            input_file: Input CSV/JSONL file path
            output_file: Output CSV file path
            error_file: Error log file path

        Returns:
            ProcessingResult: Standardized processing result
        """
        start_time = time.time()
        logger = logging.getLogger(__name__)

        try:
            # For JSONL files, we need to use convert_file method
            # Generate warning file path
            input_dir = os.path.dirname(error_file)
            input_name = os.path.splitext(os.path.basename(error_file))[0]
            warning_file = os.path.join(input_dir, f"{input_name.replace('_errors', '_warnings')}.log")

            # Use the original convert_file method
            self.convert_file(input_file, output_file, error_file, warning_file)

            execution_time = time.time() - start_time

            # Count results from output files
            success_count = 0
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    success_count = sum(1 for line in f) - 1  # Subtract header

            error_count = 0
            if os.path.exists(error_file):
                with open(error_file, 'r') as f:
                    error_count = sum(1 for line in f if line.strip())

            warning_count = 0
            if os.path.exists(warning_file):
                with open(warning_file, 'r') as f:
                    warning_count = sum(1 for line in f if line.strip())

            return ProcessingResult(
                success=True,
                total_processed=success_count + error_count,
                success_count=success_count,
                error_count=error_count,
                warning_count=warning_count,
                error_details=[],
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Processing failed: {str(e)}")
            return ProcessingResult(
                success=False,
                total_processed=0,
                success_count=0,
                error_count=0,
                warning_count=0,
                error_details=[],
                execution_time=execution_time
            )


def main():
    """Main function with standardized structure"""
    # Parse arguments
    parser = create_argument_parser()
    args = parser.parse_args()

    # Setup configuration
    config = ScriptConfig(
        batch_size=args.batch_size,
        timeout=args.timeout,
        log_level=args.log_level,
        log_dir=Path(args.log_dir) if args.log_dir else None,
        verbose=args.verbose,
        dry_run=args.dry_run
    )

    # Setup logging
    logger = setup_logging(config.log_level, config.log_dir, SCRIPT_NAME)

    try:
        # Get file paths (maintaining backward compatibility)
        input_file = args.input_file_path
        output_file = args.output_file_path
        error_file = args.error_file_path
        warning_file = args.warning_file_path

        # Validate inputs
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            sys.exit(1)

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        if config.dry_run:
            logger.info("Dry run mode - validation only")
            logger.info(f"Would process: {input_file}")
            logger.info(f"Would output to: {output_file}")
            return

        # Create converter and run (maintaining backward compatibility)
        converter = JSONLToCSVConverter()

        start_time = time.time()
        logger.info(f"Starting conversion: {input_file}")

        # Use the original convert_file method to maintain compatibility
        converter.convert_file(input_file, output_file, error_file, warning_file)

        execution_time = time.time() - start_time
        logger.info(f"Conversion completed in {execution_time:.2f} seconds")

    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
